local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local modEquipmentClass = shared.require(game.ReplicatedStorage.Library.EquipmentClass);
--==

local toolPackage = {
	ItemId=script.Name;
	Class="Tool";
	HandlerType="DeployableTool";

	Animations={
		Core={Id=**********;};
		Placing={Id=**********};
	};
	Audio={};
	Configurations={
		DisplayName = "Wall";
		DeployableType = "Wall";
		
		BuildDuration = 2;
		PlacementOffset = CFrame.Angles(0, 0, 0);
		
		ResizeToFit = true;
		StructureHealth = 500;
	};
	Properties={};
};

function toolPackage.newClass()
	return modEquipmentClass.new(toolPackage);
end

return toolPackage;