:root  > * {
    --md-primary-fg-color: #27204d; /* Header */
    --md-primary-bg-color: #ffffff; /* Header text */
    
    --md-default-fg-color: #cccccc; /* Page text color */
    --md-default-fg-color--light: #eeeeee; /* Page text */
    --md-default-fg-color--lighter:rgb(55, 59, 110); 
    --md-default-fg-color--lightest: rgb(20, 0, 110); 
    
    --md-default-bg-color: #151515; /* Page background */
    --md-default-bg-color--light: #cc4cc4;
    --md-default-bg-color--lighter: #4cc4cc;
    --md-default-bg-color--lightest: #c4cc4c;

    --md-footer-fg-color: #888888; /* Footer text */
    --md-footer-bg-color: #101010; /* Footer background */
  
    --md-accent-fg-color: #0011ff; /* Link highlight */
    --md-accent-bg-color: #cc4cc4;

    --md-code-fg-color: #888888; /* Code text unformatted */
    --md-code-bg-color: #101010; /* Code background */

    --md-code-hl-color: #cc4cc4;
    --md-code-hl-color--light: #4cc4cc;

    --md-code-hl-color: #cc4cc4; 
    --md-typeset-a-color: #d17676 !important;
    
    --md-mermaid-node-bg-color: #1c0e0e;
    --md-mermaid-node-fg-color: #7f0000;
    --md-mermaid-label-fg-color: #ffffff;
}

.md-typeset h1 {
    color: #ff8534;
}

.md-typeset h2 {
    color: #ff7a7a;
}

.md-typeset h3 {
    color: #a53ba8;
}

.md-typeset h4 {
    color: #9659ff;
}

.md-typeset h5 {
    color: #3f82ff;
}

.md-typeset code {
    border-radius: 10px;
}

.md-typeset code:not(.md-code__content) {
    border-radius: 4px;
    background-color: #3d2f2f;
    color: #a05959;
    font-weight: 600;
}

.highlighttable th.filename span.filename {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.highlighttable .linenos {
    border-bottom-left-radius: 10px;
}

.highlight span.filename {
    border-bottom: .05rem solid #505050;
}

.highlighttable .linenodiv {
    box-shadow: -.05rem 0 #505050 inset;
}

[data-md-color-scheme="slate"] {
    --md-hue: 0; 
}