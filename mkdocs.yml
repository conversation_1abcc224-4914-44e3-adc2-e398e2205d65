site_name: <PERSON><PERSON><PERSON>
site_url: https://helixnebulastudio.github.io/Almonds/
repo_url: https://github.com/HelixNebulaStudio/Almonds

nav:
  - Live: LiveBranch
  - Dev: DevBranch
  - Development: Development
  - Source: Source
  - Storyboard: Storyboard
  - Issues: "https://github.com/HelixNebulaStudio/Almonds/issues"
  - Discussions: "https://github.com/HelixNebulaStudio/Almonds/discussions"

theme:
    name: 'material'
    logo: assets/images/Almonds_Abr.svg
    icon:
      repo: fontawesome/brands/github
    font:
      text: Roboto Mono

    palette:
      - media: "(prefers-color-scheme: dark)"
        scheme: slate

    features:
      - navigation.instant
      - navigation.tracking
      - navigation.tabs
      - navigation.footer
      - navigation.path
      - navigation.prune
      - navigation.top
      - content.code.copy
      - content.code.select
      - search.share

# Extensions
markdown_extensions:
  - footnotes
  # - attr_list
  - pymdownx.arithmatex:
      generic: true
  # - pymdownx.superfences
  # - pymdownx.details
  # - pymdownx.magiclink
  - pymdownx.tasklist:
      custom_checkbox: true
  - def_list
  - pymdownx.critic
  - pymdownx.caret
  - pymdownx.keys
  - pymdownx.mark
  - pymdownx.tilde
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences

  - toc:
      permalink: true
  - attr_list
  - md_in_html


plugins:
  - tags
  - meta
  - search
  - mkdocs-nav-weight
  - include_dir_to_nav:
      file_name_as_title: true
      reverse_sort_file: true
  - roamlinks
  - obsidian-interactive-graph

extra:
  generator: false
  social:
    - icon: fontawesome/brands/mastodon
      link: https://mastodon.online/@HelixNebulaDevs
      name: Official Mastodon
    - icon: fontawesome/brands/discord
      link: https://discord.gg/Sgs39DTCQX
      name: Community Discord Server 

extra_javascript:
  - https://polyfill.io/v3/polyfill.min.js?features=es6
  - https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js
  - https://unpkg.com/mermaid/dist/mermaid.min.js 
  - https://fastly.jsdelivr.net/npm/jquery/dist/jquery.min.js
  - https://fastly.jsdelivr.net/npm/echarts/dist/echarts.min.js
  - assets/javascripts/interactive_graph.js

extra_css:
  - assets/stylesheets/extra.css
  - assets/stylesheets/interactive_graph.css