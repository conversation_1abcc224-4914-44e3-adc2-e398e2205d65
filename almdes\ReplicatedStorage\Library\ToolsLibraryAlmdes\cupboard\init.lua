local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local modEquipmentClass = shared.require(game.ReplicatedStorage.Library.EquipmentClass);
--==

local toolPackage = {
	ItemId=script.Name;
	Class="Tool";
	HandlerType="DeployableTool";

	Animations={
		Core={Id=**********;};
		Placing={Id=**********};
	};
	Audio={};
	Configurations={
		DisplayName = "Management Cupboard";
		DeployableType = "Utility";

		PlacementOffset=CFrame.new(0, 0, 0);
		
		NewInteractable = "Cupboard";
		Health = 500;
	};
	Properties={};
};

function toolPackage.newClass()
	return modEquipmentClass.new(toolPackage);
end

return toolPackage;