name: commits
on:
  push:
    branches:
      - main
jobs:
  discord_webhook:
    runs-on: ubuntu-latest
    steps:
      - name: Discord Commits
        uses: Sniddl/discord-commits@v1.6
        with:
          webhook: ${{ secrets.Discord_Webhook }}
          message: 'New updates on **Almonds**'
          embed: '{
            "title": "{{ commit.title }}",
            "description": "{{ commit.description }}\n\n[Almonds](https://helixnebulastudio.github.io/Almonds/)",
            "url": "{{ commit.url }}",
            "author": {
                "name": "{{ commit.author.name }}",
                "icon_url": "https://github.com/{{ commit.author.username }}.png"
              }
            }'
          include-extras: true