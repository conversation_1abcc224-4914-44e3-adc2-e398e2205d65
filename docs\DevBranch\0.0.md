# 0.0.x
--- 

Almonds is in development with the Revive Engine. The Almonds Datamodel is loaded in after the engine and makes use of modules and assets from the engine. 

> 0.0.1.2
- Reimplemented main menu with new engine UI.

> 0.0.0.9
- New structure, **Furnace**. Used for cooking and refining different resources.
- New item, **Matchbox**. Can be deconstructed for sulfur.

> 0.0.0.8
- Added **Play Intro** for new players.

> 0.0.0.7
- Main menu world is now new players world. Returning players will be sent to Athena City instead.
- Server browser now shows servers with player count and regions.

> 0.0.0.6
- **Small Bandit Camps** now shows up on the map.

> 0.0.0.5
- New **Menu screen**. New play screen that shows 3 methods of finding a server to play on. **Play** will spawn you in this current server, **Servers** will open the server browser, **Event** is for special event servers.

> 0.0.0.4
- Added **Tier Keys**. Found in random loots, these keys can be used to access higher tier loot crates that may be guarded by Bandits.

- Added **Key**. When key locks are placed, a key can be crafted on the lock. The key is only for the door it is crafted on, anyone with the key can access the door.
- Added **Key Lock**. Doors can now be locked using a keylock.

- Added **Cabbage Seeds**, has 4 uses, to plant cabbage on Planters.
- Added **Planter**. Used to contain growable crops.
- Added **Cabbage**. The first growable food source.

- **Workbench** is now used to unlock crafting recipes.
- Portable storages can no longer be opened unless it is equipped.
- **Crafting is now queued**, you can no longer craft multiple items at the same time.
- You can now drop items by right clicking them.

- Added **Meta Status**. **Hunger**, **Thirst** and **Comfort** are now shown in your inventory screen along with other buffs and debuffs.
- Added **Hunger**. Hunger decreases at the rate of 5 per minute. Foods now show their calorie contain that refills the hunger.
- Added **Thirst**. Thirst decreases at the rate of 7.5 per minute. Consuming items with hydration refills the thirst.

- Added **Portable Storage** items. Items such as Dufflebag, Survivors Backpack, Ammo Pouch can store items as inventory slots as long as they are equipped.

- **Building plan** now cost resources to build.
- You can now **deconstruct items by right clicking them**, deconstructing items will yield basic resources.

- Added item, **Building Plan**. Within foundations, you can plan and construct walls, window & frame walls and door frames to attach structural components to them.