name: ci
on:
  push:
    branches:
      - main
permissions:
  contents: write
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v5
        with:
          python-version: 3.x
      - uses: actions/cache@v4
        with:
          key: ${{ github.ref }}
          path: .cache
      - run: pip install mkdocs-material
      - run: pip install mkdocs-roamlinks-plugin
      - run: pip install mkdocs-mermaid2-plugin
      - run: pip install mkdocs-minify-plugin
      - run: pip install mkdocs-obsidian-interactive-graph-plugin
      - run: pip install mkdocs-nav-weight
      - run: pip install mkdocs-include-dir-to-nav
      - run: mkdocs gh-deploy --force