local Debugger = require(game.ReplicatedStorage.Library.Debugger).new(script);
--==
local RunService = game:GetService("RunService");

local modAudio = shared.require(game.ReplicatedStorage.Library.Audio);

local interactablePackage = {};
--==

function interactablePackage.init(super) -- Server/Client
    local Campfire = {
		Name = "Campfire";
        Type = "Button";
    };

    function Campfire.new(interactable: InteractableInstance, player: Player)
        local config: Configuration = interactable.Config;

        interactable.CanInteract = true;
        interactable.Label = `Use`;
    end

    function Campfire.BindInteract(interactable: InteractableInstance, info: InteractInfo)
        local config: Configuration = interactable.Config;
        if config.Parent == nil then return end;

        interactable.TypePackage.BindInteract(interactable, info);
        if info.Action == "Client" then return end;


        if info.NpcClass then
            local npcClass: NpcClass = info.NpcClass;
            return;
        end

        local player: Player? = info.Player;
        if player == nil then 
            return; 
        end;

        if RunService:IsServer() then
            local naturalLightAtt: Attachment = config.Parent:WaitForChild("PrimaryPart"):WaitForChild("_naturalLight") :: Attachment;
            interactable.Values.IsActive = not interactable.Values.IsActive;

            config:SetAttribute("IsCampfireActive", interactable.Values.IsActive);
            if interactable.Values.IsActive then
                interactable.Values.SndFire = modAudio.Play("Fire", naturalLightAtt);
            else
                modAudio.Play("FireBallBurst", naturalLightAtt);
                interactable.Values.SndFire:Destroy();
            end

            for _, obj in pairs(naturalLightAtt:GetChildren()) do
                if obj:IsA("Fire") then
                    obj.Enabled = interactable.Values.IsActive;
                elseif obj:IsA("PointLight") then
                    obj.Enabled = interactable.Values.IsActive;
                end
            end
            
            -- task.spawn(function()
            --     local overlapParams = OverlapParams.new();
            --     overlapParams.FilterDescendantsInstances = CollectionService:GetTagged("HealthCompRootParts");
            --     overlapParams.MaxParts = 16;

            --     while interactable.Values.IsActive do
            --         local rootParts = workspace:GetPartBoundsInRadius(naturalLightAtt.WorldPosition, 16);

            --         if not workspace:IsAncestorOf(config) then break; end;
            --         task.wait(1);
            --     end
            -- end)
        end
    end
    
    -- When interactable pops up on screen.
    function Campfire.BindPrompt(interactable: InteractableInstance, info: InteractInfo)
        if RunService:IsServer() then return end;
        
        local clientData = info.ClientData;
        if clientData == nil then return end;

    end


    super.registerPackage(Campfire);

end

return interactablePackage;

